<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化管理系统</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <link rel="stylesheet" th:href="@{/css/i18n.css}">
    <style>
        :root {
            --el-color-primary: #3C89CF;
            --el-color-primary-light-3: #6ba3d9;
            --el-color-primary-light-5: #8bb8e3;
            --el-color-primary-light-7: #abcdec;
            --el-color-primary-light-8: #bbd6f0;
            --el-color-primary-light-9: #cce2f5;
            --el-color-primary-dark-2: #3077b8;
        }

        .query-form-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            white-space: nowrap;
        }

        .query-form-item .form-label {
            min-width: 80px;
            margin-right: 8px;
            margin-bottom: 0;
            font-weight: 500;
            color: #606266;
        }

        .query-form-item .form-control {
            flex: 1;
            min-width: 0;
        }

        .el-card {
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .el-button--primary {
            background-color: #3C89CF;
            border-color: #3C89CF;
        }

        .el-button--primary:hover {
            background-color: #6ba3d9;
            border-color: #6ba3d9;
        }
    </style>
</head>
<body>
    <div id="app" class="container-fluid">
        <!-- 头部标题 -->
        <div class="page-header mb-4">
            <h2 class="page-title">
                <i class="el-icon-s-grid" style="margin-right: 8px; color: #3C89CF;"></i>
                国际化信息管理
            </h2>
        </div>

        <!-- 查询条件 -->
        <div class="el-card query-section">
            <div class="el-card__body">
                <form id="queryForm" class="query-form">
                    <div class="query-form-row">
                        <div class="query-form-item">
                            <span class="form-label">语言标签:</span>
                            <input type="text" class="form-control el-input__inner" id="languageTag" name="languageTag" placeholder="请输入语言标签">
                        </div>
                        <div class="query-form-item">
                            <span class="form-label">国际化标识:</span>
                            <input type="text" class="form-control el-input__inner" id="code" name="code" placeholder="请输入标识">
                        </div>
                        <div class="query-form-item">
                            <span class="form-label">文本值:</span>
                            <input type="text" class="form-control el-input__inner" id="message" name="message" placeholder="请输入文本">
                        </div>
                        <div class="query-form-buttons">
                            <button type="button" class="el-button el-button--primary" onclick="queryData()">
                                查询
                            </button>
                            <button type="button" class="el-button el-button--default" onclick="resetQuery()">
                                重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="el-card table-container">
            <!-- 操作按钮栏 -->
            <div class="el-card__header table-header">
                <div class="table-title">
                    <i class="el-icon-s-grid" style="margin-right: 8px; color: #3C89CF;"></i>
                    数据列表
                </div>
                <div class="table-actions">
                    <button type="button" class="el-button el-button--default el-button--small" onclick="exportData()">
                        导出
                    </button>
                    <button type="button" class="el-button el-button--default el-button--small" onclick="showImportModal()">
                        导入
                    </button>
                    <button type="button" class="el-button el-button--primary el-button--small" onclick="showAddModal()">
                        新增
                    </button>
                    <button type="button" class="el-button el-button--default el-button--small" onclick="resetQuery()">
                        刷新
                    </button>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="el-card__body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="el-table table">
                        <thead class="el-table__header">
                            <tr>
                                <th width="50" class="el-table__cell text-center">ID</th>
                                <th class="el-table__cell text-left">语言标签</th>
                                <th class="el-table__cell text-left">国际化标识</th>
                                <th class="el-table__cell text-left">文本值</th>
                                <th class="el-table__cell text-left">备注</th>
                                <th class="el-table__cell text-center">创建时间</th>
                                <th class="el-table__cell text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody" class="el-table__body">
                            <!-- 数据行将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="el-pagination-container">
                    <nav aria-label="分页导航">
                        <ul class="el-pagination" id="pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </ul>
                    </nav>
                    <div class="pagination-info" id="paginationInfo">
                        第 1-10 条/共 58 条
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="el-dialog-wrapper" id="editModal" style="display: none;">
        <div class="el-dialog">
            <div class="el-dialog__header">
                <span class="el-dialog__title" id="editModalTitle">新增国际化信息</span>
                <button type="button" class="el-dialog__headerbtn" onclick="closeEditModal()">
                    <i class="el-icon-close"></i>
                </button>
            </div>
            <div class="el-dialog__body">
                <form id="editForm" class="el-form">
                    <input type="hidden" id="editId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="el-form-item">
                                <label for="editApplicationName" class="el-form-item__label">应用名称 <span class="el-form-item__required">*</span></label>
                                <div class="el-form-item__content">
                                    <input type="text" class="el-input__inner" id="editApplicationName" name="applicationName" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="el-form-item">
                                <label for="editLanguageTag" class="el-form-item__label">语言标签 <span class="el-form-item__required">*</span></label>
                                <div class="el-form-item__content">
                                    <input type="text" class="el-input__inner" id="editLanguageTag" name="languageTag" required placeholder="如：zh-CN, en-US">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="el-form-item">
                                <label for="editCode" class="el-form-item__label">国际化标识 <span class="el-form-item__required">*</span></label>
                                <div class="el-form-item__content">
                                    <input type="text" class="el-input__inner" id="editCode" name="code" required placeholder="如：menu.account.settings">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="el-form-item">
                                <label for="editMessage" class="el-form-item__label">文本内容</label>
                                <div class="el-form-item__content">
                                    <textarea class="el-textarea__inner" id="editMessage" name="message" rows="3" placeholder="支持占位符 {0}, {1}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="el-form-item">
                                <label for="editRemark" class="el-form-item__label">备注</label>
                                <div class="el-form-item__content">
                                    <textarea class="el-textarea__inner" id="editRemark" name="remark" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="el-dialog__footer">
                <button type="button" class="el-button el-button--default" onclick="closeEditModal()">取消</button>
                <button type="button" class="el-button el-button--primary" onclick="saveData()">确定</button>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div class="el-dialog-wrapper" id="importModal" style="display: none;">
        <div class="el-dialog">
            <div class="el-dialog__header">
                <span class="el-dialog__title">导入国际化数据</span>
                <button type="button" class="el-dialog__headerbtn" onclick="closeImportModal()">
                    <i class="el-icon-close"></i>
                </button>
            </div>
            <div class="el-dialog__body">
                <form id="importForm" enctype="multipart/form-data" class="el-form">
                    <div class="el-form-item">
                        <label for="importFile" class="el-form-item__label">选择Excel文件</label>
                        <div class="el-form-item__content">
                            <input type="file" class="el-input__inner" id="importFile" name="file" accept=".xlsx,.xls" required>
                        </div>
                    </div>
                    <div class="el-alert el-alert--info">
                        <div class="el-alert__content">
                            <span class="el-alert__title">
                                <i class="el-icon-info"></i>
                                导入说明：
                            </span>
                            <div class="el-alert__description">
                                <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                                    <li>支持.xlsx和.xls格式的Excel文件</li>
                                    <li>第一行必须是标题行：应用名称、语言标签、国际化标识、文本内容、备注</li>
                                    <li>应用名称、语言标签、国际化标识为必填字段</li>
                                    <li>如果数据已存在，将会更新现有记录</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="el-dialog__footer">
                <button type="button" class="el-button el-button--default" onclick="closeImportModal()">取消</button>
                <button type="button" class="el-button el-button--primary" onclick="importData()">导入</button>
            </div>
        </div>
    </div>

    <!-- Element Plus JS -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script th:src="@{/js/i18n.js}"></script>

    <script>
        // Element Plus 模态框控制函数
        function showAddModal() {
            document.getElementById('editModal').style.display = 'flex';
            document.getElementById('editModalTitle').textContent = '新增国际化信息';
            document.getElementById('editForm').reset();
            document.getElementById('editId').value = '';
        }

        function showImportModal() {
            document.getElementById('importModal').style.display = 'flex';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            queryData();

            // 点击模态框背景关闭
            document.getElementById('editModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditModal();
                }
            });

            document.getElementById('importModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImportModal();
                }
            });
        });
    </script>
</body>
</html>
