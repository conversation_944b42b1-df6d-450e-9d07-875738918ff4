/* 国际化管理系统样式 - Element Plus 风格 */

/* 全局样式 */
body {
    background-color: #f5f7fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #303133;
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

/* 容器样式 */
.container-fluid {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Element Plus 主题色覆盖 */
:root {
    --el-color-primary: #3C89CF;
    --el-color-primary-light-3: #6ba3d9;
    --el-color-primary-light-5: #8bb8e3;
    --el-color-primary-light-7: #abcdec;
    --el-color-primary-light-8: #bbd6f0;
    --el-color-primary-light-9: #cce2f5;
    --el-color-primary-dark-2: #3077b8;
}

/* Element Plus 卡片样式 */
.el-card {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fff;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.el-card__header {
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    padding: 18px 20px;
    font-weight: 500;
    color: #303133;
}

.el-card__body {
    padding: 20px;
}

/* 查询区域样式 */
.query-section {
    margin-bottom: 8px;
}

.query-form-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: nowrap;
}

.query-form-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.query-form-item .form-label {
    min-width: 80px;
    margin-right: 8px;
    margin-bottom: 0;
    font-weight: 500;
    color: #606266;
    flex-shrink: 0;
}

.query-form-item .form-control,
.query-form-item .el-input__inner {
    width: 180px;
    min-width: 180px;
}

/* Element Plus 表格样式 */
.table-container {
    overflow: hidden;
}

.el-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    font-size: 14px;
}

.el-table__header th,
.el-table__cell {
    border-top: none;
    border-bottom: 1px solid #ebeef5;
    font-weight: 500;
    background-color: #fafafa;
    color: #909399;
    vertical-align: middle;
    padding: 12px 15px;
    font-size: 13px;
    text-align: left;
}

.el-table__body td {
    vertical-align: middle;
    padding: 12px 15px;
    border-bottom: 1px solid #ebeef5;
    color: #606266;
    background-color: #fff;
}

.el-table__body tr:hover td {
    background-color: #f5f7fa;
}

.el-table__body tr:last-child td {
    border-bottom: none;
}

/* 表格对齐样式 */
.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

/* Element Plus 按钮样式 */
.el-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    padding: 8px 15px;
    border: 1px solid transparent;
    transition: all 0.3s;
    cursor: pointer;
    text-decoration: none;
    white-space: nowrap;
    outline: none;
    margin: 0;
    background: #fff;
    color: #606266;
    border-color: #dcdfe6;
}

.el-button:hover {
    color: #3C89CF;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.el-button--primary {
    background-color: #3C89CF;
    border-color: #3C89CF;
    color: #fff;
}

.el-button--primary:hover {
    background-color: #6ba3d9;
    border-color: #6ba3d9;
    color: #fff;
}

.el-button--default {
    background-color: #fff;
    border-color: #dcdfe6;
    color: #606266;
}

.el-button--default:hover {
    color: #3C89CF;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.el-button--small {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px;
}

.el-button i {
    margin-right: 4px;
}

.el-button i:only-child {
    margin-right: 0;
}

/* 操作按钮组 */
.action-buttons {
    margin-bottom: 20px;
}

.action-buttons .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Element Plus 表单样式 */
.el-input__inner,
.el-textarea__inner,
.form-control {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding: 8px 12px;
    font-size: 14px;
    color: #606266;
    background-color: #fff;
    transition: border-color 0.3s;
    width: 100%;
    box-sizing: border-box;
    outline: none;
}

.el-input__inner:focus,
.el-textarea__inner:focus,
.form-control:focus {
    border-color: #3C89CF;
    box-shadow: 0 0 0 2px rgba(60, 137, 207, 0.2);
    outline: none;
}

.el-input__inner::placeholder,
.el-textarea__inner::placeholder,
.form-control::placeholder {
    color: #c0c4cc;
}

.el-form-item__label,
.form-label {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
    font-size: 14px;
    display: block;
}

.el-form-item__required {
    color: #f56c6c;
}

.el-form-item {
    margin-bottom: 18px;
}

.el-form-item__content {
    position: relative;
}

/* 输入框组样式 */
.input-group {
    margin-bottom: 18px;
}

.input-group .form-control {
    margin-bottom: 0;
}

/* Element Plus 对话框样式 */
.el-dialog-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.el-dialog {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    min-width: 500px;
}

.el-dialog__header {
    padding: 20px 24px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0;
}

.el-dialog__headerbtn {
    background: none;
    border: none;
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.el-dialog__headerbtn:hover {
    color: #3C89CF;
}

.el-dialog__body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.el-dialog__footer {
    padding: 16px 24px;
    background-color: #fafafa;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* Element Plus 分页样式 */
.el-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
}

.el-pagination {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 4px;
    list-style: none;
    padding: 0;
}

.el-pagination .page-item {
    margin: 0;
    list-style: none;
}

.el-pagination .page-link {
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    min-width: 32px;
    text-align: center;
    background-color: #fff;
    transition: all 0.3s;
    cursor: pointer;
    text-decoration: none;
}

.el-pagination .page-link:hover {
    color: #3C89CF;
    background-color: #ecf5ff;
    border-color: #b3d8ff;
}

.el-pagination .page-item.active .page-link {
    background-color: #3C89CF;
    border-color: #3C89CF;
    color: #fff;
}

.el-pagination .page-item.disabled .page-link {
    color: #c0c4cc;
    background-color: #fff;
    border-color: #ebeef5;
    cursor: not-allowed;
}

/* 分页信息 */
.pagination-info {
    color: #909399;
    font-size: 13px;
}

/* 页面头部样式 */
.page-header {
    margin-bottom: 20px;
}

.page-title {
    font-size: 20px;
    font-weight: 500;
    color: #303133;
    margin: 0;
}

/* 表格头部样式 */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

.table-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    display: flex;
    align-items: center;
}

.table-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.table-actions .el-button {
    margin: 0;
}

/* 操作按钮样式 */
.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    margin-right: 4px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.action-btn-edit {
    color: #409eff;
    border: 1px solid #b3d8ff;
    background-color: #ecf5ff;
}

.action-btn-edit:hover {
    background-color: #409eff;
    color: #fff;
    text-decoration: none;
}

.action-btn-delete {
    color: #f56c6c;
    border: 1px solid #fbc4c4;
    background-color: #fef0f0;
}

.action-btn-delete:hover {
    background-color: #f56c6c;
    color: #fff;
    text-decoration: none;
}

/* Element Plus 提示框样式 */
.el-alert {
    border-radius: 4px;
    border: 1px solid;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
}

.el-alert--info {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
}

.el-alert--success {
    background-color: #f0f9ff;
    border-color: #c6f7d0;
    color: #67c23a;
}

.el-alert--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    color: #e6a23c;
}

.el-alert--error {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
}

.el-alert__content {
    flex: 1;
}

.el-alert__title {
    font-weight: 500;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.el-alert__title i {
    margin-right: 6px;
}

.el-alert__description {
    font-size: 13px;
    line-height: 1.5;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* 工具提示样式 */
[title] {
    cursor: help;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 文本截断 */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 状态标签 */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* 图标样式 */
.bi {
    vertical-align: -0.125em;
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 操作按钮组 */
.btn-group-sm > .btn, .btn-sm {
    margin-right: 0.25rem;
}

.btn-group-sm > .btn:last-child, .btn-sm:last-child {
    margin-right: 0;
}

/* 表单验证样式 */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* 成功状态 */
.is-valid {
    border-color: #67c23a;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #67c23a;
}

/* 空状态优化 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.empty-state p {
    font-size: 14px;
    margin: 0;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .query-section {
        padding: 15px;
    }

    .query-form .row > div {
        margin-bottom: 12px;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .table-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .table-responsive {
        font-size: 13px;
    }

    .table th,
    .table td {
        padding: 8px 6px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .action-btn {
        font-size: 11px;
        padding: 2px 6px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 表格行号样式 */
.table tbody tr td:first-child {
    font-weight: 500;
    color: #909399;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-active {
    background-color: #67c23a;
}

.status-inactive {
    background-color: #f56c6c;
}

.status-pending {
    background-color: #e6a23c;
}

/* 移除图标样式，使用纯文字按钮 */

/* 操作按钮优化 */
.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    margin-right: 4px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
    border: 1px solid;
}

.action-btn-edit {
    color: #3C89CF;
    border-color: #b3d8ff;
    background-color: #ecf5ff;
}

.action-btn-edit:hover {
    background-color: #3C89CF;
    color: #fff;
    text-decoration: none;
}

.action-btn-delete {
    color: #f56c6c;
    border-color: #fbc4c4;
    background-color: #fef0f0;
}

.action-btn-delete:hover {
    background-color: #f56c6c;
    color: #fff;
    text-decoration: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .query-form-row {
        flex-direction: column;
        gap: 12px;
    }

    .query-form-item {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }

    .query-form-item .form-label {
        min-width: auto;
        margin-bottom: 4px;
        margin-right: 0;
    }

    .query-form-item .form-control,
    .query-form-item .el-input__inner {
        width: 100%;
        min-width: auto;
    }

    .query-form-buttons {
        width: 100%;
        justify-content: center;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .table-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .el-dialog {
        min-width: 90vw;
        margin: 20px;
    }
}
